"use client"

import Link from "next/link"
import Image from "next/image"
import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Mail } from "lucide-react"

interface NewsItem {
  id: number
  documentId: string
  title: string
  newsbody: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  Classification: string
}

interface NewsResponse {
  data: NewsItem[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

export default function NewsDetailPage() {
  const params = useParams()
  const [newsItem, setNewsItem] = useState<NewsItem | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchNewsItem = async () => {
      try {
        setLoading(true)
        // First, try to fetch by documentId
        const response = await fetch(
          `http://43.153.145.176:1337/api/news2?filters[documentId][$eq]=${params.id}`
        )
        
        if (!response.ok) {
          throw new Error('Failed to fetch news item')
        }
        
        const data: NewsResponse = await response.json()
        
        if (data.data.length > 0) {
          setNewsItem(data.data[0])
        } else {
          setError('記事が見つかりません')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchNewsItem()
    }
  }, [params.id])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '.')
  }

  const getClassificationLabel = (classification: string) => {
    return classification.replace(/^info/, '')
  }

  const formatContent = (content: string) => {
    return content.split('\n').map((line, index) => (
      <p key={index} className="mb-4">
        {line}
      </p>
    ))
  }

  if (loading) {
    return (
      <div className="min-h-screen">
        <div className="py-12 bg-white">
          <div className="container-huari">
            <div className="text-center py-12">
              <p className="text-gray-500">読み込み中...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !newsItem) {
    return (
      <div className="min-h-screen">
        <div className="py-12 bg-white">
          <div className="container-huari">
            <div className="text-center py-12">
              <p className="text-red-500">{error || '記事が見つかりません'}</p>
              <Link 
                href="/news" 
                className="mt-4 inline-block text-huari-red hover:underline"
              >
                お知らせ一覧に戻る
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <div className="relative w-full h-[300px] overflow-hidden">
        <Image
          src="/images/news/n1.png"
          alt="お知らせ"
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black/50 flex items-center">
          <div className="container-huari">
            <h1 className="text-3xl md:text-4xl font-bold text-white">新着情報</h1>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-white py-3 border-b border-gray-200">
        <div className="container-huari">
          <nav className="flex text-sm">
            <Link href="/" className="text-gray-500 hover:text-huari-red transition-colors">
              HOME
            </Link>
            <span className="mx-2 text-gray-400">&gt;</span>
            <Link href="/news" className="text-gray-500 hover:text-huari-red transition-colors">
              新着情報
            </Link>
            <span className="mx-2 text-gray-400">&gt;</span>
            <span className="text-gray-700">{newsItem.title}</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-12 bg-white">
        <div className="container-huari">
          <div className="max-w-4xl mx-auto">
            <article>
              {/* Article Header */}
              <header className="mb-8">
                <div className="mb-4">
                  <p className="text-sm text-gray-500">
                    {formatDate(newsItem.publishedAt)}
                    {newsItem.Classification && (
                      <span className="ml-2 text-xs bg-gray-200 text-gray-700 px-2 py-0.5 rounded">
                        {getClassificationLabel(newsItem.Classification)}
                      </span>
                    )}
                  </p>
                </div>
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                  {newsItem.title}
                </h1>
              </header>

              {/* Article Content */}
              <div className="prose prose-lg max-w-none">
                <div className="text-gray-700 leading-relaxed">
                  {formatContent(newsItem.newsbody)}
                </div>
              </div>

              {/* Back to News List */}
              <div className="mt-12 pt-8 border-t border-gray-200">
                <Link 
                  href="/news" 
                  className="inline-flex items-center text-huari-red hover:underline transition-colors"
                >
                  ← 新着情報一覧に戻る
                </Link>
              </div>
            </article>
          </div>
        </div>
      </div>

      {/* Contact section - full width gray background like original */}
      <div className="bg-gray-100 py-12">
        <div className="container-huari text-center">
          <h3 className="text-2xl font-bold mb-2">CONTACT US</h3>
          <p className="text-lg font-bold mb-8">お見積り・ご相談はこちら</p>

          <div className="mb-2">
            <a href="tel:0723612111" className="text-2xl font-bold text-gray-800 hover:text-huari-red transition-colors">
              +(86) 0574-86118978
            </a>
          </div>
          <p className="text-sm text-gray-600 mb-8">
            営業時間 9：00～17：00（土日祝 定休）
          </p>

          <Link
            href="/contact"
            className="inline-flex items-center gap-2 bg-huari-red text-white px-8 py-3 hover:bg-huari-red/90 transition-colors font-medium"
          >
            <Mail size={24} className="mr-2" />
            お問い合わせ
          </Link>
        </div>
      </div>
    </div>
  )
}
