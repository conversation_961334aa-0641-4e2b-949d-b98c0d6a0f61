"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { ChevronDown, ChevronRight, ArrowUp } from "lucide-react"

export default function TechnologyPage() {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({})
  const [showBackToTop, setShowBackToTop] = useState(false)

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }))
  }

  // Smooth scroll to section
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  // Back to top functionality
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  // Show/hide back to top button based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 400)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <div className="relative w-full h-[300px] overflow-hidden">
        <Image
          src="/images/technology/t1.jpg"
          alt="製造技術"
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black/50 flex items-center">
          <div className="container-huari">
            <h1 className="text-3xl md:text-4xl font-bold text-white">製造技術</h1>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-white py-3 border-b border-gray-200">
        <div className="container-huari">
          <nav className="flex text-sm">
            <Link href="/" className="text-gray-500 hover:text-primary transition-colors">
              HOME
            </Link>
            <span className="mx-2 text-gray-400">&gt;</span>
            <span className="text-gray-700">製造技術</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-12 bg-white">
        <div className="container-huari">
          <h2 className="text-2xl font-bold text-center mb-8">技術コラム・技術資料</h2>
          <p className="text-center text-gray-700 mb-12 max-w-3xl mx-auto">
            圧鋳技術の基本原理から応用まで、専門的な技術情報を体系的にご紹介します。
            製造技術の向上と品質改善にお役立てください。
          </p>

          {/* Table of Contents */}
          <div className="bg-gray-50 p-6 rounded-lg mb-12">
            <h3 className="text-xl font-bold mb-6 text-center">目次</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <button onClick={() => scrollToSection('section-1')} className="block text-left text-huari-red hover:underline transition-colors">1. 高真空圧鋳技術</button>
                <button onClick={() => scrollToSection('section-2')} className="block text-left text-huari-red hover:underline transition-colors">2. アルミ圧鋳金型の設計原則</button>
                <button onClick={() => scrollToSection('section-3')} className="block text-left text-huari-red hover:underline transition-colors">3. 圧鋳成型技術の基本原理</button>
                <button onClick={() => scrollToSection('section-4')} className="block text-left text-huari-red hover:underline transition-colors">4. 圧鋳工芸基本流程</button>
                <button onClick={() => scrollToSection('section-5')} className="block text-left text-huari-red hover:underline transition-colors">5. 圧鋳常用金属材料</button>
                <button onClick={() => scrollToSection('section-6')} className="block text-left text-huari-red hover:underline transition-colors">6. 圧鋳技術難点</button>
              </div>
              <div className="space-y-2">
                <button onClick={() => scrollToSection('section-7')} className="block text-left text-huari-red hover:underline transition-colors">7. 圧鋳技術中の常見問題</button>
                <button onClick={() => scrollToSection('section-8')} className="block text-left text-huari-red hover:underline transition-colors">8. 圧鋳金型の寿命</button>
                <button onClick={() => scrollToSection('section-9')} className="block text-left text-huari-red hover:underline transition-colors">9. 常用表面処理類型</button>
                <button onClick={() => scrollToSection('section-10')} className="block text-left text-huari-red hover:underline transition-colors">10. 一体化圧鋳技術について</button>
                <button onClick={() => scrollToSection('section-11')} className="block text-left text-huari-red hover:underline transition-colors">11. 表面処理の優位性</button>
              </div>
            </div>
          </div>

          {/* Section 1: High Vacuum Die Casting Technology */}
          <div id="section-1" className="mb-12">
            <div className="bg-huari-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-huari-red h-8 w-8 flex items-center justify-center rounded-full mr-3">1</span>
                高真空圧鋳技術
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <p className="text-gray-700 leading-relaxed">
                高真空圧鋳技術は、鋳物内部の気孔率を効果的に低減し、鋳物内部組織を緻密にし、その機械的性能を向上させ、充填条件を改善し、製品の引張強度、降伏強度、延伸率などを向上させることができます。金型保護の面では、型腔内の反圧力を最大限に減少させ、射出比圧を低下させ、圧鋳金型の使用寿命を延長するのに役立ち、圧鋳部品の使用範囲を拡大する重要な圧鋳技術です。
              </p>
            </div>
          </div>

          {/* Section 2: Aluminum Die Casting Mold Design Principles */}
          <div id="section-2" className="mb-12">
            <div className="bg-huari-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-huari-red h-8 w-8 flex items-center justify-center rounded-full mr-3">2</span>
                アルミ圧鋳金型の設計原則
              </h2>
            </div>

            {/* Section 2.1 */}
            <div className="mb-8">
              <button
                onClick={() => toggleSection('section-2-1')}
                className="w-full bg-gray-100 hover:bg-gray-200 p-4 rounded-lg flex items-center justify-between transition-colors"
              >
                <h3 className="text-lg font-semibold text-left">2.1 製品品質要求を満たす</h3>
                {expandedSections['section-2-1'] ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
              </button>

              {expandedSections['section-2-1'] && (
                <div className="mt-4 space-y-6">
                  {/* Section 2.1.1 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-huari-red">2.1.1 分型面選択原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>鋳物品質の保証：</strong>分型面は鋳物の最大輪郭部に選択し、脱型に便利で鋳物が開型後に動型側に留まるようにし、順調な取り出しを確保する必要があります。同時に、鋳物の重要な表面（配合面、外観面など）に分型面を設置することを避け、バリ、毛刺などの鋳物品質に影響する欠陥の発生を防ぐ必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>金型構造の簡素化：</strong>できるだけ平面分型面を採用し、複雑な曲面分型を避けて、金型加工の難易度とコストを低減します。曲面分型を採用する必要がある場合は、曲面の加工精度を確保し、金型合型の密封性を保証する必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>排気に有利：</strong>分型面は型腔内ガスの排出に有利であるべきで、排気溝の開設などの方式により、ガスが圧鋳過程で順調に排出され、気孔などの欠陥の発生を減少させることができます。例えば、分型面の縁部や鋳物の最後充填部位に排気溝を設置し、その深さは一般的に0.05-0.1mm、幅は鋳物の大きさによって決まり、通常5-20mmです。
                    </p>
                  </div>

                  {/* Section 2.1.2 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-huari-red">2.1.2 型腔と型芯設計原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>寸法精度：</strong>鋳物の寸法公差要求に基づき、型腔と型芯の寸法を合理的に確定します。アルミ合金の収縮率（一般的に0.8%-1.2%）、および金型使用過程での摩耗状況を考慮し、適当な加工余量と公差を予約する必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>脱型勾配：</strong>鋳物の脱型を便利にするため、型腔と型芯表面には一定の脱型勾配を設置する必要があります。脱型勾配の大きさは鋳物の壁厚、形状と合金の特性によって決まります。一般的に、アルミ合金圧鋳部品の脱型勾配は1°-3°の間です。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>強度と剛性：</strong>型腔と型芯は十分な強度と剛性を持ち、圧鋳過程での高圧と熱応力に耐える必要があります。合理的な金型材料の選択（H13鋼などの熱間工具鋼）、金型壁厚の増加または補強リブの設置などの方式により、その強度と剛性を向上させることができます。
                    </p>
                  </div>

                  {/* Section 2.1.3 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-huari-red">2.1.3 注湯システム設計原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>金属液流動の円滑性：</strong>注湯システムは金属液が迅速、均一に型腔を充填できることを保証し、渦流、飛散などの現象の発生を避け、空気の巻き込みと酸化介在物の発生を防ぐ必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>型芯への衝撃回避：</strong>注湯口の設置は金属液が直接型芯に衝撃することを避け、型芯が沖刷により摩耗や変形することを防ぎ、鋳物の寸法精度と品質に影響することを避ける必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>注湯口除去の便利性：</strong>注湯口の形状と位置は鋳物脱型後の注湯口除去に便利で、鋳物に損傷を与えないようにする必要があります。一般的に、注湯口の厚さは鋳物の壁厚より小さくし、機械加工や人工打撃の方式で除去しやすくします。
                    </p>
                  </div>

                  {/* Section 2.1.4 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-huari-red">2.1.4 排溢システム設計原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>効果的な排気：</strong>排溢システムは型腔内のガス、介在物と冷汚金属液を効果的に排出し、鋳物内部に気孔、冷間隔離などの欠陥が発生することを避ける必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>冷汚金属液の貯蔵：</strong>溢流槽は冷汚金属液を貯蔵する作用も果たし、冷汚金属液が鋳物の重要部位に進入し、鋳物の品質に影響することを防ぎます。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>清理の便利性：</strong>溢流槽と排気槽の設計は鋳物脱型後にその中の金属滓と雑物を清理するのに便利であるべきで、除去しやすい構造形式を採用できます。
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Section 2.2 */}
            <div className="mb-8">
              <button
                onClick={() => toggleSection('section-2-2')}
                className="w-full bg-gray-100 hover:bg-gray-200 p-4 rounded-lg flex items-center justify-between transition-colors"
              >
                <h3 className="text-lg font-semibold text-left">2.2 金型製造可能性の確保</h3>
                {expandedSections['section-2-2'] ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
              </button>

              {expandedSections['section-2-2'] && (
                <div className="mt-4 space-y-6">
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-huari-red">2.2.1 金型構造簡素化原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>部品数量の削減：</strong>金型機能を満たす前提で、できるだけ金型構造を簡素化し、金型部品の数量を削減します。これにより金型の加工難易度とコストを低減し、金型の組立精度と信頼性を向上させることができます。例えば、一体式の型腔や型芯を採用し、過度な嵌合構造を避けます。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>複雑な加工工芸の回避：</strong>金型設計時はできるだけ常規の加工工芸を採用し、フライス削、旋削、穴あけなどを使用し、過度に複雑な特殊加工工芸（放電加工、ワイヤーカット加工など）の採用を避けます。これにより金型の加工コストと周期を低減できます。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>標準化設計：</strong>できるだけ標準部品と通用部品を採用し、金型フレーム、押出ピン、ガイド柱、ガイドスリーブなどを使用します。標準化設計は金型の製造効率を向上させ、コストを低減するだけでなく、金型の保守と部品交換にも便利です。
                    </p>
                  </div>

                  {/* Section 2.2.2 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-huari-red">2.2.2 加工工芸性原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>加工設備の考慮：</strong>企業の既存加工設備と加工能力に基づいて金型設計を行い、金型の各部品が既存設備で加工できることを確保します。例えば、企業が高精度五軸加工センターを持たない場合、金型設計時に過度に複雑な三次元曲面構造の出現を避けるべきです。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>合理的な公差配合：</strong>金型部品の公差配合を設計する際は、加工精度と組立要求を考慮する必要があります。公差が大きすぎると金型の使用性能に影響し、公差が小さすぎると加工難易度とコストが増加します。一般的に、金型部品の配合公差はその機能と使用要求に基づいて合理的に確定すべきで、例えばガイド柱とガイドスリーブの配合公差はH7/h6を採用できます。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>加工操作の便利性：</strong>金型部品の設計は加工操作に便利であるべきで、穴、溝などの構造を加工する際は、工具のアクセス性と加工空間を考慮する必要があります。同時に、深穴、深溝などの加工困難な構造の出現を避け、避けられない場合は適切な加工工芸と措置を採用すべきです。
                    </p>
                  </div>

                  {/* Section 2.2.3 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-huari-red">2.2.3 組立と調整便利性原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>合理的な組立順序：</strong>金型設計時は合理的な組立順序を考慮し、金型の組立過程を簡単、便利にします。一般的に、まず金型の主要部品（型腔、型芯、金型フレームなど）を組立し、その後その他の補助部品（押出機構、抜芯機構など）を組立します。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>位置決めと締結の便利性：</strong>金型部品間の位置決めと締結は信頼性があり、同時に分解と交換に便利である必要があります。ピン位置決め、ボルト締結などの方式を採用し、金型使用過程で緩みや位置ずれが発生しないことを確保します。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>調整と保守の便利性：</strong>金型は調整と保守に便利な構造に設計すべきで、必要な観察孔、テスト孔などを設置し、金型調整と使用過程で問題を適時発見し処理できるようにします。同時に、損傷や摩耗しやすい部品（押出ピン、注湯口スリーブなど）は分解可能な構造に設計し、交換に便利にします。
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Section 2.3 */}
          <div className="mb-8">
            <button
              onClick={() => toggleSection('section-2-3')}
              className="w-full bg-gray-100 hover:bg-gray-200 p-4 rounded-lg flex items-center justify-between transition-colors"
            >
              <h3 className="text-lg font-semibold text-left">2.3 金型寿命の向上</h3>
              {expandedSections['section-2-3'] ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
            </button>

            {expandedSections['section-2-3'] && (
              <div className="mt-4 space-y-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-md font-semibold mb-3 text-huari-red">2.3.1 金型材料選択原則</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>使用性能要求の満足：</strong>アルミ圧鋳金型の作業条件と性能要求に基づき、適切な金型材料を選択します。アルミ圧鋳金型は通常高温、高圧、高速金属液の沖刷と反復的な熱循環に耐える必要があるため、金型材料は良好な熱疲労性能、高温強度、硬度、耐摩耗性と耐食性などを持つ必要があります。常用の金型材料にはH13鋼、SKD61鋼などの熱間工具鋼があり、より高い要求の金型には粉末冶金金型鋼なども採用できます。
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>コスト要因の考慮：</strong>使用性能要求を満たす前提で、金型材料のコストを考慮する必要があります。異なる金型材料の価格差は大きく、金型の生産ロット、使用寿命要求などの要因を総合的に考慮し、コストパフォーマンスの高い材料を選択する必要があります。例えば、生産ロットが小さい金型には価格が相対的に低い普通熱間工具鋼を選択でき、生産ロットが大きく使用寿命要求が高い金型には性能がより良いが価格も高い粉末冶金金型鋼を選択できます。
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    <strong>材料の加工性：</strong>金型材料の加工性も材料選択時に考慮すべき要因の一つです。材料は良好な切削加工性能、熱処理性能と放電加工性能などを持ち、金型の加工と製造に便利である必要があります。例えば、H13鋼は良好な総合性能と加工性を持ち、アルミ圧鋳金型によく使用される材料の一つです。
                  </p>
                </div>

                {/* Section 2.3.2 */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-md font-semibold mb-3 text-huari-red">2.3.2 熱平衡設計原則</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>合理的な冷却システム：</strong>金型の過熱による性能低下と寿命短縮を避けるため、合理的な冷却システムを設計すべきです。冷却システムは金型の温度を効果的に制御し、金型が圧鋳過程で適切な温度範囲を保持できるようにする必要があります。一般的に、アルミ圧鋳金型の冷却水路はできるだけ型腔と型芯表面に近づけ、その直径は通常6-12mm、間隔は鋳物の壁厚と形状によって決まり、一般的に30-50mmです。
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>均一な温度分布：</strong>冷却システムの設計は金型表面温度分布の均一性を保証し、局部過熱や過冷却の状況の出現を避ける必要があります。冷却水路の走向と形式を合理的に配置し、直通式、螺旋式または随形冷却水路などを採用して、金型表面温度の均一分布を実現できます。同時に、冷却水路の密封性に注意し、冷却水の漏れを防ぐ必要があります。
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    <strong>熱膨張補償：</strong>金型は作業過程で温度変化の影響を受けて熱膨張を発生するため、金型設計時に熱膨張の補償を考慮する必要があります。熱膨張間隙の予約、弾性要素や熱補償構造の採用などの方式により、熱膨張の金型への影響を緩和し、金型が熱応力により変形や亀裂を発生することを避けることができます。
                  </p>
                </div>

                {/* Section 2.3.3 */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-md font-semibold mb-3 text-huari-red">2.3.3 応力集中回避原則</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>丸角遷移：</strong>金型の構造設計において、できるだけ尖角と鋭辺の出現を避け、丸角遷移の方式を採用すべきです。丸角は応力集中を効果的に減少させ、金型の抗疲労性能と使用寿命を向上させることができます。例えば、型腔と型芯の縁部、金型の転角部などの部位には、適当な大きさの丸角を設置すべきで、一般的に丸角半径は0.5-5mm、具体的な大きさは金型の構造と寸法によって決まります。
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>合理的な構造設計：</strong>金型の構造は合理的に設計し、局部受力過大の状況の出現を避ける必要があります。例えば、抜芯機構、押出機構などの部品を設計する際は、その受力の均一性を確保し、局部受力過大による金型損傷を避ける必要があります。同時に、金型部品間の接続方式に注意し、応力集中の発生を避ける必要があります。
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    <strong>金型レイアウトの最適化：</strong>金型の全体レイアウトは合理的であるべきで、金型が作業過程で受ける力が均一に分布できるようにします。例えば、多型腔金型については、各型腔の配列方式と寸法の合理性を確保し、金属液が型腔を充填する際に発生する圧力が金型に均一に作用し、受力不均による金型変形や損傷を避ける必要があります。
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Section 2.4 */}
          <div className="mb-8">
            <button
              onClick={() => toggleSection('section-2-4')}
              className="w-full bg-gray-100 hover:bg-gray-200 p-4 rounded-lg flex items-center justify-between transition-colors"
            >
              <h3 className="text-lg font-semibold text-left">2.4 生産効率の向上</h3>
              {expandedSections['section-2-4'] ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
            </button>

            {expandedSections['section-2-4'] && (
              <div className="mt-4 space-y-6">
                {/* Section 2.4.1 */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-md font-semibold mb-3 text-huari-red">2.4.1 押出機構設計原則</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>十分な押出力：</strong>押出機構は十分な押出力を提供し、鋳物が順調に脱型できることを確保する必要があります。押出力の大きさは鋳物の形状、寸法、壁厚とアルミ合金の収縮力などの要因によって決まります。一般的に、押出力は経験公式により計算でき、実際の調整により確定することもできます。
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>均一な押出分布：</strong>押出ピンの分布は均一であるべきで、鋳物が脱型過程で受力が均一になり、変形や損傷の出現を避けます。押出ピンの数量と位置は鋳物の形状と構造に基づいて合理的に設計すべきで、一般的に押出ピンは鋳物の壁厚が厚い、形状が複雑または粘型しやすい部位に分布させるべきです。
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>押出行程の合理性：</strong>押出機構の押出行程は合理的であるべきで、鋳物が順調に脱型できることを保証すると同時に、押出ピンが過度に伸出して金型を損傷したり生産効率に影響したりすることを避ける必要があります。押出行程は一般的に鋳物の高さより5-10mm大きくすべきです。
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    <strong>信頼性と安定性：</strong>押出機構は良好な信頼性と安定性を持ち、長期使用過程で押出ピンの詰まり、破断などの故障が発生しないようにする必要があります。優質な押出ピン材料、合理的な押出ピン配合間隙と信頼性のある押出駆動方式（液圧駆動、機械駆動など）を採用して、押出機構の信頼性と安定性を保証できます。
                  </p>
                </div>

                {/* Section 2.4.2 */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-md font-semibold mb-3 text-huari-red">2.4.2 抜芯機構設計原則</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>抜芯力計算の正確性：</strong>抜芯機構を設計する際は、抜芯力を正確に計算し、適切な抜芯方式と抜芯機構を選択する必要があります。抜芯力の大きさは鋳物の形状、寸法、アルミ合金の収縮力および抜芯距離などの要因によって決まります。一般的に、経験公式や有限要素解析などの方法により抜芯力を計算できます。
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>抜芯方式の合理的選択：</strong>鋳物の構造と生産要求に基づき、抜芯方式を合理的に選択します。斜ピン抜芯、スライダー抜芯、液圧抜芯などがあります。斜ピン抜芯は構造が簡単でコストが低く、抜芯距離が小さい場合に適用されます；スライダー抜芯は抜芯距離が大きく、抜芯力が大きい場合に適用されます；液圧抜芯は抜芯距離が大きく、抜芯力が大きく、かつ抜芯動作の精確な制御が必要な場合に適用されます。
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>抜芯動作の信頼性：</strong>抜芯機構の動作は信頼性があるべきで、圧鋳過程で抜芯不到位や抜芯過早などの問題が発生しないようにする必要があります。抜芯機構の各部品間の配合精度と運動の柔軟性を保証し、同時に必要な制限装置と締結装置を設置し、抜芯動作の正確性と信頼性を確保する必要があります。
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    <strong>抜芯機構の保守便利性：</strong>抜芯機構は保守と修理に便利な構造に設計すべきで、スライダーのガイドレールは分解と交換が容易であるべきで、斜ピンの取付けと調整は便利であるべきです。これにより抜芯機構の保守コストと停機時間を低減し、生産効率を向上させることができます。
                  </p>
                </div>

                {/* Section 2.4.3 */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-md font-semibold mb-3 text-huari-red">2.4.3 自動化生産適応性原則</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>自動化設備との互換性：</strong>金型の設計は自動化生産設備との互換性を考慮すべきで、圧鋳機の取出ロボット、自動化噴霧設備などとの適合性を確保する必要があります。金型の構造と寸法は自動化設備の操作要求を満たし、自動化設備による取出、脱型剤噴霧などの操作に便利であるべきです。
                  </p>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    <strong>オンライン検査の便利性：</strong>生産過程の自動化検査を実現するため、金型の設計はオンライン検査装置の設置に便利であるべきで、センサー、カメラなどの設置を考慮する必要があります。例えば、金型の適当な位置に温度センサーを設置し、金型の温度変化をリアルタイムで監視したり、カメラを設置して鋳物の成型過程を監視し、問題を適時発見し処理できるようにします。
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    <strong>金型の迅速交換：</strong>頻繁な金型交換が必要な生産シーンについては、金型の設計は迅速交換に便利であるべきです。標準化された金型取付けインターフェース、迅速位置決めと締結装置などを採用し、金型交換の時間と労働強度を減少させ、生産効率を向上させることができます。
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Section 3: Basic Principles of Die Casting Technology */}
          <div id="section-3" className="mb-12">
            <div className="bg-huari-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-huari-red h-8 w-8 flex items-center justify-center rounded-full mr-3">3</span>
                圧鋳成型技術の基本原理
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex flex-col lg:flex-row gap-6 items-start">
                <div className="lg:w-2/3">
                  <p className="text-gray-700 leading-relaxed">
                    圧鋳成型技術は、溶融金属材料を高圧で金型型腔に高速注入し、冷却凝固により部品を形成する製造工芸です。その基本原理には、溶融金属の高速充填、精密な金型設計と製造、合理的な冷却システム、および脱型後の後処理技術などの核心的な環節が含まれます。重力鋳造過程において、高圧の増大に伴い、溶融金属は高速条件下で型腔を完全に充満し、気孔や収縮孔などの鋳造欠陥を著しく減少させ、製品の密度と機械性能を向上させることができます。
                  </p>
                </div>
                <div className="lg:w-1/3">
                  <div className="w-full rounded-lg overflow-hidden border border-gray-200">
                    <Image
                      src="/images/technology/t3.png"
                      alt="圧鋳成型技術の基本原理"
                      width={400}
                      height={300}
                      className="w-full h-auto object-contain"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 4: Basic Process Flow */}
          <div id="section-4" className="mb-12">
            <div className="bg-huari-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-huari-red h-8 w-8 flex items-center justify-center rounded-full mr-3">4</span>
                圧鋳工芸基本流程
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex flex-col lg:flex-row gap-6 items-start">
                <div className="lg:w-2/3">
                  <p className="text-gray-700 leading-relaxed">
                    圧鋳工芸の基本流程には、材料準備、金型予熱、溶融注入、冷却凝固、脱型、後処理などの段階が含まれます。各段階は製品の最終品質に重要な影響を与えるため、厳格な工芸パラメータ制御が必要です。
                  </p>
                </div>
                <div className="lg:w-1/3">
                  <div className="w-full rounded-lg overflow-hidden border border-gray-200">
                    <Image
                      src="/images/technology/t4.png"
                      alt="圧鋳工芸基本流程"
                      width={400}
                      height={300}
                      className="w-full h-auto object-contain"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 5: Common Metal Materials */}
          <div id="section-5" className="mb-12">
            <div className="bg-huari-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-huari-red h-8 w-8 flex items-center justify-center rounded-full mr-3">5</span>
                圧鋳常用金属材料
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3 text-huari-red">アルミニウム合金</h4>
                  <ul className="space-y-2 text-gray-700">
                    <li>• ADC12、A380（軽量化、耐腐食、自動車部品に広く応用）</li>
                    <li>• A360、ADC6（高流動性+耐腐食性）</li>
                    <li>• ADC10</li>
                    <li>• Al-Si-Cu系合金（AlSi12Cu1Fe）</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-3 text-huari-red">亜鉛合金</h4>
                  <ul className="space-y-2 text-gray-700">
                    <li>• Zamak2、Zamak3、Zamak5</li>
                    <li>• 融点が低く、成型性が良好</li>
                    <li>• 小型精密部品に適用</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Section 6: Technical Difficulties */}
          <div id="section-6" className="mb-12">
            <div className="bg-huari-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-huari-red h-8 w-8 flex items-center justify-center rounded-full mr-3">6</span>
                圧鋳技術難点
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex flex-col lg:flex-row gap-6 items-start">
                <div className="lg:w-2/3">
                  <h4 className="font-semibold mb-4 text-huari-red">核心難点：</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2 text-gray-700">
                      <p>① 金属液流動と充填制御</p>
                      <p>② 金型熱管理と寿命制御</p>
                    </div>
                    <div className="space-y-2 text-gray-700">
                      <p>③ 気孔と収縮欠陥制御</p>
                      <p>④ 薄壁部品と複雑構造成型</p>
                    </div>
                  </div>
                </div>
                <div className="lg:w-1/3">
                  <div className="w-full rounded-lg overflow-hidden border border-gray-200">
                    <Image
                      src="/images/technology/t6.png"
                      alt="圧鋳技術難点"
                      width={400}
                      height={300}
                      className="w-full h-auto object-contain"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 7: Common Problems */}
          <div id="section-7" className="mb-12">
            <div className="bg-huari-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-huari-red h-8 w-8 flex items-center justify-center rounded-full mr-3">7</span>
                圧鋳技術中の常見問題
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-gray-700">
                <div className="space-y-2">
                  <p>① 気孔</p>
                  <p>② 収縮孔/収縮</p>
                </div>
                <div className="space-y-2">
                  <p>③ 亀裂</p>
                  <p>④ 冷間隔離</p>
                </div>
                <div className="space-y-2">
                  <p>⑤ 欠鋳</p>
                  <p>⑥ 流痕</p>
                </div>
                <div className="space-y-2">
                  <p>⑦ 凹陥</p>
                  <p>⑧ 網状毛刺</p>
                </div>
                <div className="space-y-2">
                  <p>⑨ 色斑</p>
                  <p>⑩ 麻面</p>
                </div>
              </div>
            </div>
          </div>

          {/* Section 8: Mold Lifespan */}
          <div id="section-8" className="mb-12">
            <div className="bg-huari-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-huari-red h-8 w-8 flex items-center justify-center rounded-full mr-3">8</span>
                圧鋳金型の寿命
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <p className="text-gray-700 leading-relaxed">
                圧鋳金型の寿命は多種の要因の影響を受け、通常「万回」を単位として計算され、異なる材料と工芸条件下での寿命差は顕著です。金型の寿命は材料選択、設計品質、使用条件、保守管理などの要因と密接に関係しています。
              </p>
            </div>
          </div>

          {/* Section 9: Surface Treatment Types */}
          <div id="section-9" className="mb-12">
            <div className="bg-huari-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-huari-red h-8 w-8 flex items-center justify-center rounded-full mr-3">9</span>
                常用表面処理類型
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-gray-700">
                <div className="space-y-2">
                  <p>① 硬質酸化</p>
                  <p>② 塗装</p>
                </div>
                <div className="space-y-2">
                  <p>③ 電泳</p>
                  <p>④ サンドブラスト</p>
                </div>
                <div className="space-y-2">
                  <p>⑤ メッキ</p>
                  <p>⑥ 振動研磨</p>
                </div>
                <div className="space-y-2">
                  <p>⑦ 粉体塗装</p>
                  <p>⑧ フッ素コーティング</p>
                </div>
              </div>
            </div>
          </div>

          {/* Section 10: Integrated Die Casting Technology */}
          <div id="section-10" className="mb-12">
            <div className="bg-huari-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-huari-red h-8 w-8 flex items-center justify-center rounded-full mr-3">10</span>
                一体化圧鋳技術について
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="font-semibold mb-4 text-huari-red">一体化圧鋳技術の特点と優位性：</h4>
              <div className="space-y-4 text-gray-700 leading-relaxed">
                <p>
                  <strong>製造効率の顕著な向上：</strong>該技術は多工程を単一工程に統合し、生産周期を大幅に短縮し、労働コストを低減します。高精度金型及び制御システムの応用により、製品の一致性と外観品質が向上します。
                </p>
                <p>
                  <strong>製品性能の全面的向上：</strong>一体化圧鋳技術により製品はより優れた機械性能と耐腐食性能を持ち、軽量化設計を実現し、現代工業の高性能と高信頼性の要求を満たします。
                </p>
                <p>
                  <strong>環境保護型脱型剤及び噴塗技術の積極的応用：</strong>環境に配慮した製造プロセスを実現し、持続可能な発展に貢献します。
                </p>
              </div>
            </div>
          </div>

          {/* Section 11: Surface Treatment Advantages */}
          <div id="section-11" className="mb-12">
            <div className="bg-huari-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-huari-red h-8 w-8 flex items-center justify-center rounded-full mr-3">11</span>
                表面処理の優位性
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3 text-huari-red">欠陥の隠蔽</h4>
                  <p className="text-gray-700 mb-4">気孔、収縮孔修補、流痕などの圧鋳原生欠陥を効果的に隠蔽します。</p>

                  <h4 className="font-semibold mb-3 text-huari-red">機能性向上</h4>
                  <p className="text-gray-700">耐腐食性の飛躍的向上、耐摩耗性の向上、導熱/電気特性の最適化を実現します。</p>
                </div>
                <div>
                  <h4 className="font-semibold mb-3 text-huari-red">美観と価値ブランド</h4>
                  <p className="text-gray-700 mb-4">色彩、標識などの美観性を向上させ、製品の付加価値を高めます。</p>

                  <h4 className="font-semibold mb-3 text-huari-red">工芸協同効果</h4>
                  <p className="text-gray-700">前処理の重要な作用、複合工芸組合によるコストと環境保護のバランスを実現します。</p>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action Section */}
          <div className="bg-huari-red text-white p-6 md:p-8 rounded-md">
            <div className="text-center">
              <h3 className="text-xl font-bold mb-4">技術相談・お問い合わせ</h3>
              <p className="mb-6">
                圧鋳技術に関するご質問やご相談がございましたら、お気軽にお問い合わせください。
                専門技術者が詳しくご説明いたします。
              </p>
              <Link
                href="/contact"
                className="inline-flex items-center bg-white text-huari-red px-6 py-3 rounded-sm hover:bg-gray-100 transition-colors font-medium"
              >
                お問い合わせはこちら
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-2"><path d="m9 18 6-6-6-6"></path></svg>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Back to Top Button */}
      {showBackToTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 bg-huari-red text-white p-3 rounded-full shadow-lg hover:bg-huari-red/90 transition-all duration-300 z-50"
          aria-label="ページトップに戻る"
        >
          <ArrowUp size={20} />
        </button>
      )}
    </div>
  )
}
