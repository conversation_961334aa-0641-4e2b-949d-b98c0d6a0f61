"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { useSearchParams, useRouter } from "next/navigation"
import { getAllProducts, Product } from "@/lib/productUtils"
import { ProductCard } from "./ProductCard"
import { ProductSearch, SearchFilters } from "./ProductSearch"
import { Mail } from "lucide-react"

const PRODUCTS_PER_PAGE = 30

export function ProductPageContent() {
  const [allProducts, setAllProducts] = useState<Product[]>([])
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [isLoading, setIsLoading] = useState(true)
  const searchParams = useSearchParams()
  const router = useRouter()

  useEffect(() => {
    // Load products on component mount
    const loadProducts = () => {
      try {
        const products = getAllProducts()
        setAllProducts(products)

        // 检查URL参数并应用初始搜索
        const urlCategory = searchParams.get('category')
        const urlKeyword = searchParams.get('keyword')
        const urlSearchText = searchParams.get('searchText')

        if (urlCategory || urlKeyword || urlSearchText) {
          // 应用来自URL的搜索条件
          const initialFilters: SearchFilters = {
            category: urlCategory || '',
            keyword: urlKeyword || '',
            searchText: urlSearchText || ''
          }
          applyFilters(products, initialFilters)
        } else {
          setFilteredProducts(products)
        }
      } catch (error) {
        console.error("Error loading products:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadProducts()
  }, [searchParams])

  // 提取过滤逻辑为独立函数
  const applyFilters = (products: Product[], filters: SearchFilters) => {
    let filtered = products

    // 按分类筛选
    if (filters.category) {
      filtered = filtered.filter(product =>
        product.category === filters.category
      )
    }

    // 按预设关键词筛选（在产品名称中搜索）
    if (filters.keyword) {
      filtered = filtered.filter(product =>
        product.name.includes(filters.keyword)
      )
    }

    // 按自由文本搜索（在产品名称和分类中搜索）
    if (filters.searchText) {
      filtered = filtered.filter(product =>
        product.name.includes(filters.searchText) ||
        product.category.includes(filters.searchText)
      )
    }

    setFilteredProducts(filtered)
    setCurrentPage(1)
  }

  const handleSearch = (filters: SearchFilters) => {
    // 更新URL参数
    const newSearchParams = new URLSearchParams()
    if (filters.category) newSearchParams.set('category', filters.category)
    if (filters.keyword) newSearchParams.set('keyword', filters.keyword)
    if (filters.searchText) newSearchParams.set('searchText', filters.searchText)

    const newUrl = newSearchParams.toString()
      ? `/product?${newSearchParams.toString()}`
      : '/product'

    router.push(newUrl, { scroll: false })

    // 应用过滤器
    applyFilters(allProducts, filters)
  }

  const handleReset = () => {
    // 清除URL参数
    router.push('/product', { scroll: false })

    // 重置过滤器
    setFilteredProducts(allProducts)
    setCurrentPage(1)
  }

  // Pagination logic
  const totalPages = Math.ceil(filteredProducts.length / PRODUCTS_PER_PAGE)
  const startIndex = (currentPage - 1) * PRODUCTS_PER_PAGE
  const endIndex = startIndex + PRODUCTS_PER_PAGE
  const currentProducts = filteredProducts.slice(startIndex, endIndex)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-24">
        <div className="container-huari py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-huari-red mx-auto"></div>
            <p className="mt-4 text-gray-600">製品データを読み込み中...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-24">
      <div className="container-huari py-8">
        {/* Breadcrumb */}
        <nav className="mb-6">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-huari-red transition-colors">
              HOME
            </Link>
            <span>•</span>
            <span className="text-gray-900 font-medium">製作事例</span>
          </div>
        </nav>

        {/* Page Title */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">製作事例</h1>
        </div>

        {/* Search Section */}
        <div className="mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">実績を絞り込む</h2>
          <p className="text-gray-700 mb-6">
            検索したい要素を選択し、「検索」を押すと近しい実績をご覧になれます。
          </p>
          <ProductSearch onSearch={handleSearch} onReset={handleReset} />
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            {filteredProducts.length}件の製作事例が見つかりました
          </p>
        </div>

        {/* Products Grid */}
        {currentProducts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            {currentProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-600 text-lg">
              検索条件に一致する製品が見つかりませんでした。
            </p>
            <button
              onClick={handleReset}
              className="mt-4 text-huari-red hover:underline"
            >
              すべての製品を表示
            </button>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center space-x-1 mt-8">
            {/* Show first few pages, current page area, and last few pages */}
            {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
              let pageNum: number
              if (totalPages <= 5) {
                pageNum = i + 1
              } else if (currentPage <= 3) {
                pageNum = i + 1
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i
              } else {
                pageNum = currentPage - 2 + i
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => handlePageChange(pageNum)}
                  className={`px-3 py-1 text-sm transition-colors ${
                    currentPage === pageNum
                      ? "bg-huari-red text-white font-bold"
                      : "text-huari-red hover:underline"
                  }`}
                >
                  {pageNum}
                </button>
              )
            })}

            {/* Show next page link if not on last page */}
            {currentPage < totalPages && (
              <>
                <span className="text-gray-400">...</span>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  className="text-huari-red hover:underline text-sm px-2"
                >
                  次へ
                </button>
              </>
            )}
          </div>
        )}


        {/* Contact Section */}
        <div className="bg-white border border-gray-200 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            CONTACT US
          </h2>
          <p className="text-lg text-gray-700 mb-6">
            お見積り・ご相談はこちら
          </p>

          <div className="mb-6">
            <a
              href="tel:0723612111"
              className="text-3xl font-bold text-huari-red hover:text-huari-red/80 transition-colors"
            >
              +(86) 0574-86118978
            </a>
            <p className="text-sm text-gray-600 mt-1">
              営業時間 9：00～17：00（土日祝 定休）
            </p>
          </div>

          <Link
            href="/contact"
            className="inline-flex items-center bg-huari-red text-white px-8 py-3 rounded-md hover:bg-huari-red/90 transition-colors font-medium"
          >
            <Mail size={24} className="mr-2" />
            お問い合わせ
          </Link>
        </div>
      </div>
    </div>
  )
}
